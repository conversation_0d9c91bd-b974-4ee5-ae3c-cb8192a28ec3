export default defineNuxtPlugin(() => {
  if (process.dev) {
    console.log('Connecting to Figma MCP SSE...');

    const eventSource = new EventSource('http://127.0.0.1:3845/sse');

    eventSource.onopen = () => {
      console.log('Figma MCP SSE connection opened.');
    };

    eventSource.onmessage = (event) => {
      console.log('Received message from Figma MCP:', event.data);
      try {
        const data = JSON.parse(event.data);
        console.log('Parsed Figma MCP data:', data);
      } catch (error) {
        // Data is not JSON, just log as text
      }
    };

    eventSource.onerror = (error) => {
      console.error('Figma MCP SSE error:', error);
      eventSource.close();
    };
  }
});

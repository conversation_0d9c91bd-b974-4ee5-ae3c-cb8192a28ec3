<template>
  <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto" @click="handleBackdropClick">
    <!-- Backdrop -->
    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"></div>

    <!-- Modal -->
    <div class="flex min-h-full items-center justify-center p-4">
      <div 
        class="relative bg-white rounded-lg shadow-lg w-full" 
        :class="modalSizeClass"
        @click.stop
      >
        <!-- Modal Header with blue background -->
        <div class="bg-[#0066B3] rounded-t-lg px-[22px] py-[17px] h-[79px] flex items-center justify-between">
          <h3 class="text-white text-[28px] font-bold leading-[1.2]">
            {{ title }}
          </h3>
          <button 
            v-if="showCloseButton"
            @click="closeModal" 
            class="text-white hover:text-gray-200 w-5 h-5 transition-colors"
          >
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M15 5L5 15M5 5L15 15" stroke="currentColor" stroke-width="4" stroke-linecap="round"
                stroke-linejoin="round" />
            </svg>
          </button>
        </div>

        <!-- Modal Body -->
        <div class="py-[40px] pt-[28px] px-5" :class="bodyClass">
          <slot />
        </div>

        <!-- Modal Footer (optional) -->
        <div v-if="$slots.footer" class="px-[22px] pb-[28px]">
          <slot name="footer" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  isOpen: boolean
  title: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  showCloseButton?: boolean
  closeOnBackdrop?: boolean
  bodyClass?: string
}

interface Emits {
  close: []
  open: []
  backdropClick: []
}

const props = withDefaults(defineProps<Props>(), {
  size: 'lg',
  showCloseButton: true,
  closeOnBackdrop: true,
  bodyClass: ''
})

const emit = defineEmits<Emits>()

// Computed property for modal size classes
const modalSizeClass = computed(() => {
  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg', 
    lg: 'max-w-[855px]',
    xl: 'max-w-4xl',
    full: 'max-w-full mx-4'
  }
  return sizeClasses[props.size]
})

const closeModal = () => {
  emit('close')
}

const handleBackdropClick = () => {
  emit('backdropClick')
  if (props.closeOnBackdrop) {
    closeModal()
  }
}

// Watch for isOpen changes to emit open event
watch(() => props.isOpen, (newValue) => {
  if (newValue) {
    emit('open')
  }
})

// Close modal on Escape key
onMounted(() => {
  const handleEscape = (e: KeyboardEvent) => {
    if (e.key === 'Escape' && props.isOpen) {
      closeModal()
    }
  }

  document.addEventListener('keydown', handleEscape)

  onUnmounted(() => {
    document.removeEventListener('keydown', handleEscape)
  })
})

// Prevent body scroll when modal is open
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    document.body.style.overflow = 'hidden'
  } else {
    document.body.style.overflow = ''
  }
})

// Cleanup on unmount
onUnmounted(() => {
  document.body.style.overflow = ''
})
</script>

<style scoped>
/* Custom focus styles */
.focus\:border-\[\#0066B3\]:focus {
  border-color: #0066B3;
}



/* Animation for modal */
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

/* Smooth transitions */
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
</style>

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  devtools: { enabled: true },

  // Đăng ký các modules
  modules: [
    '@nuxtjs/tailwindcss',
    '@pinia/nuxt',
  ],

  // C<PERSON><PERSON> hình CSS global
  css: [
    '~/assets/css/main.css',
  ],

  // Cấu hình Pinia
  pinia: {
    storesDirs: ['./stores/**'],
  },

  // Quản lý biến môi trường
  runtimeConfig: {
    public: {
      apiBaseUrl: process.env.NUXT_PUBLIC_API_BASE_URL,
      apiKey: process.env.NUXT_PUBLIC_API_KEY,
    }
  },

  // Cấu hình Nitro để xử lý CORS trong development
  nitro: {
    devProxy: {
      '/api/': {
        target: process.env.NUXT_PUBLIC_API_BASE_URL || 'https://api-bhck-app-stag.vivas.vn',
        changeOrigin: true,
        prependPath: true,
        headers: {
          'x-api-key': process.env.NUXT_PUBLIC_API_KEY || 'BTC128M8093ILKR584ERM147IAGJ2IX2'
        }
      }
    }
  },

  // Cấu hình CORS cho development
  devServer: {
    port: 3000
  }
})
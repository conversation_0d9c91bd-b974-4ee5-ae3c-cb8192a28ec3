<template>
  <div class="min-h-screen relative">
    <!-- Header -->
    <Header />
    
    <!-- Main content -->
    <main class="relative">
      <slot />
    </main>
    
    <!-- Footer -->
    <Footer />
  </div>
</template>

<script setup lang="ts">
// This is the default layout that will be used by all pages
// unless they specify a different layout
</script>

<style scoped>
/* Ensure the layout takes full height */
html, body {
  height: 100%;
}
</style>
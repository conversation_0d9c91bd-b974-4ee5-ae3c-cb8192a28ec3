# Hướng dẫn chức năng Mua bảo hiểm

## Tổng quan

Chức năng mua bảo hiểm được xây dựng theo quy trình 3 bước như mô tả trong tài liệu URD, bao gồm:

1. **SCR-1**: <PERSON><PERSON> báo thông tin mua bảo hiểm
2. **SCR-2**: <PERSON><PERSON><PERSON> nhận thông tin
3. **SCR-3**: Thanh toán QR code
4. **SCR-4**: Thông báo kết quả mua hàng

## Cấu trúc Files

```
stores/
└── insurance.ts                    # Pinia store quản lý trạng thái

pages/
└── insurance/
    ├── index.vue                   # Trang giới thiệu và bắt đầu
    ├── step1.vue                   # SCR-1: <PERSON><PERSON> báo thông tin
    ├── step2.vue                   # SCR-2: X<PERSON><PERSON> nhận thông tin
    ├── step3.vue                   # SCR-3: <PERSON><PERSON> toán
    └── step4.vue                   # SCR-4: <PERSON><PERSON><PERSON> quả

composables/
└── useInsuranceApi.ts              # API functions cho bảo hiểm
```

## Quy trình sử dụng

### 1. B<PERSON><PERSON> đầu từ trang chủ

- Người dùng click nút **"Mua bảo hiểm"** trên trang chủ
- Hệ thống kiểm tra trạng thái đăng nhập
- Nếu chưa đăng nhập: hiển thị yêu cầu đăng nhập
- Nếu đã đăng nhập: chuyển đến `/insurance`

### 2. Trang giới thiệu (/insurance)

- Hiển thị quy trình 3 bước
- Thông tin về bảo hiểm và phí
- Nút "Bắt đầu mua bảo hiểm" → chuyển đến Step 1

### 3. Step 1: Khai báo thông tin (/insurance/step1)

#### Thông tin chủ xe (bắt buộc):
- Tên công ty (max 200 ký tự)
- Mã số thuế (max 200 ký tự)
- Địa chỉ công ty
- Số điện thoại (10 chữ số)
- Email nhận GCN (định dạng email)
- Checkbox: Lưu thông tin chủ xe

#### Thông tin xe:
- Biển kiểm soát (bắt buộc, max 200 ký tự)
- Số chỗ ngồi (bắt buộc, 1-9)
- Số khung (không bắt buộc, max 200 ký tự)
- Số máy (không bắt buộc, max 200 ký tự)
- Trọng tải (disabled, hiển thị "Trên 15 tấn")
- Loại xe (disabled, hiển thị "Xe ô tô chở hàng (Xe tải)")
- Mục đích sử dụng (disabled, hiển thị "Kinh doanh vận tải")
- Ngày bắt đầu (datetime picker)
- Ngày kết thúc (disabled, tự động = ngày bắt đầu + 30 ngày)

#### Validation:
- Tất cả trường bắt buộc phải được điền
- Email phải đúng định dạng
- Số điện thoại phải có đúng 10 chữ số
- Số chỗ ngồi phải từ 1-9

### 4. Step 2: Xác nhận thông tin (/insurance/step2)

#### Hiển thị:
- Thông tin chủ xe (readonly)
- Thông tin xe (readonly)
- Thời hạn bảo hiểm
- **Phí bảo hiểm** (cố định):
  - Phí chưa VAT: 266.666đ
  - Thuế VAT: 26.667đ
  - Tổng phí (gồm VAT): 293.333đ
  - **TỔNG PHÍ THANH TOÁN**: 293.333đ

#### Checkbox xác nhận:
- "Tôi đồng ý đã đọc, hiểu các quy định Pháp luật về Bảo hiểm bắt buộc..."
- Link "Xem quy định bảo hiểm" mở modal

#### Hành động:
- Nút "Thanh toán" chỉ enable khi checkbox được chọn
- Click "Thanh toán" → gọi API `v1/orders/buy-insurance` → chuyển Step 3

### 5. Step 3: Thanh toán (/insurance/step3)

#### Hiển thị:
- Thông tin đơn hàng (mã đơn hàng, biển số xe, tên công ty, số tiền)
- QR Code thanh toán (tích hợp Epay)
- Hướng dẫn thanh toán 5 bước
- Thông tin chuyển khoản thủ công
- Trạng thái thanh toán (pending/success/failed)
- Đếm ngược thời gian (15 phút)

#### Xử lý:
- Tự động kiểm tra trạng thái thanh toán mỗi 5 giây
- Khi thanh toán thành công → chuyển Step 4
- Khi hết thời gian hoặc thất bại → hiển thị trạng thái lỗi

### 6. Step 4: Kết quả (/insurance/step4)

#### Thành công:
- Icon tick xanh
- Thông tin đơn hàng
- Các bước tiếp theo (nhận GCN qua email)
- Thông tin liên hệ hỗ trợ

#### Thất bại:
- Icon X đỏ
- Thông tin lỗi
- Hướng dẫn thử lại
- Thông tin liên hệ hỗ trợ

## API Integration

### 1. API Mua bảo hiểm

**Endpoint**: `POST /v1/orders/buy-insurance`

**Request Body**:
```json
{
  "company_name": "Vivas",
  "company_address": "string",
  "tax_number": "11234",
  "phone_number": "0123456789",
  "email_gcn": "<EMAIL>",
  "isUpdateInfo": true,
  "bien_so_xe": "123456",
  "so_cho_ngoi": 0,
  "so_khung": "121",
  "so_may": "221",
  "trong_tai": 1000,
  "loai_xe": "Xe may",
  "md_su_dung": "Cho hang",
  "tg_bat_dau": 1753691847,
  "tg_ket_thuc": 1753991847
}
```

**Response**:
```json
{
  "success": true,
  "statusCode": 0,
  "code": 0,
  "message": "Khởi tạo đơn hàng thành công",
  "data": {
    "order_id": 5394
  }
}
```

### 2. Composable useInsuranceApi

Cung cấp các functions:
- `createInsuranceOrder()`: Tạo đơn hàng
- `checkPaymentStatus(orderId)`: Kiểm tra trạng thái thanh toán
- `generatePaymentQR(orderId)`: Tạo QR code
- `getOrderDetails(orderId)`: Lấy chi tiết đơn hàng

## Store Management

### Insurance Store (Pinia)

**State**:
- `currentStep`: Bước hiện tại (1-4)
- `ownerInfo`: Thông tin chủ xe
- `vehicleInfo`: Thông tin xe
- `insuranceFee`: Phí bảo hiểm (cố định)
- `isAgreed`: Trạng thái checkbox đồng ý
- `orderId`: ID đơn hàng từ API
- `paymentStatus`: Trạng thái thanh toán
- `isLoading`: Trạng thái loading

**Getters**:
- `isStep1Valid`: Kiểm tra step 1 hợp lệ
- `isStep2Valid`: Kiểm tra step 2 hợp lệ
- `getApiPayload`: Dữ liệu gửi API
- `formattedFees`: Format số tiền hiển thị

**Actions**:
- `updateOwnerInfo()`: Cập nhật thông tin chủ xe
- `updateVehicleInfo()`: Cập nhật thông tin xe
- `nextStep()`, `previousStep()`: Điều hướng bước
- `resetStore()`: Reset về trạng thái ban đầu
- `initializeFromUser()`: Khởi tạo từ user đã đăng nhập

## Validation Rules

### Step 1 Validation (Zod Schema):
- `company_name`: required, max 200 chars
- `tax_number`: required, max 200 chars
- `company_address`: required
- `phone_number`: required, exactly 10 digits
- `email_gcn`: required, valid email format
- `bien_so_xe`: required, max 200 chars
- `so_cho_ngoi`: required, 1-9
- `so_khung`: optional, max 200 chars
- `so_may`: optional, max 200 chars

### Step 2 Validation:
- Checkbox đồng ý phải được chọn

## Testing

### Manual Testing Steps:

1. **Test đăng nhập**: Đảm bảo chỉ user đã đăng nhập mới có thể mua bảo hiểm
2. **Test Step 1**: Kiểm tra validation form, auto-fill từ user info
3. **Test Step 2**: Kiểm tra hiển thị thông tin, modal quy định, checkbox
4. **Test Step 3**: Kiểm tra QR code, countdown, payment status
5. **Test Step 4**: Kiểm tra hiển thị kết quả success/failed
6. **Test Navigation**: Kiểm tra điều hướng giữa các bước
7. **Test API**: Kiểm tra gọi API tạo đơn hàng

### Test Data:

**User login**: <EMAIL> / password123

**Test vehicle info**:
- Biển số: 30A-12345
- Số chỗ ngồi: 5
- Số khung: ABC123
- Số máy: DEF456

## Deployment Notes

1. Đảm bảo API endpoint `/v1/orders/buy-insurance` hoạt động
2. Cấu hình Epay integration cho QR code thực tế
3. Setup email service để gửi GCN bảo hiểm
4. Kiểm tra responsive design trên mobile
5. Test performance với nhiều user đồng thời

## Support

- **Hotline**: 1900 1234
- **Email**: <EMAIL>
- **Giờ làm việc**: 8:00 - 17:00 (Thứ 2 - Thứ 6)

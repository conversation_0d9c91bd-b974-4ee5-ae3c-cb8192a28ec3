<template>
  <div class="min-h-screen bg-white">
    <!-- Header -->
    <header class="w-full bg-white shadow-sm">
      <div class="max-w-[1920px] mx-auto px-[390px] py-4">
        <div class="flex items-center justify-between">
          <!-- Left side with back button and logo -->
          <div class="flex items-center gap-6">
            <button class="flex items-center justify-center w-6 h-6">
              <img src="~/assets/images/navigate-before-icon.svg" alt="Back" class="w-2 h-3" />
            </button>
            <div class="flex items-center">
              <NuxtLink to="/" class="relative w-[130px] h-[32px] block hover:opacity-80 transition-opacity">
                <img src="~/assets/images/logo-vector-1.svg" alt="Logo 1" class="absolute" style="left: 15.69px; top: 12.58px; width: 13.99px; height: 17.08px;" />
                <img src="~/assets/images/logo-vector-2.svg" alt="Logo 2" class="absolute" style="left: 0.99px; top: 0px; width: 29.62px; height: 31.76px;" />
                <img src="~/assets/images/logo-vector-3.svg" alt="Logo 3" class="absolute" style="left: 34.58px; top: 7.94px; width: 20.19px; height: 15.78px;" />
                <img src="~/assets/images/logo-vector-4.svg" alt="Logo 4" class="absolute" style="left: 61px; top: 7.94px; width: 18.7px; height: 15.78px;" />
                <img src="~/assets/images/logo-vector-5.svg" alt="Logo 5" class="absolute" style="left: 89.57px; top: 7.94px; width: 17.55px; height: 15.88px;" />
                <img src="~/assets/images/logo-vector-6.svg" alt="Logo 6" class="absolute" style="left: 111.46px; top: 7.94px; width: 17.55px; height: 15.83px;" />
              </NuxtLink>
            </div>
          </div>
          
          <!-- Navigation -->
          <div class="flex items-center gap-8">
            <a href="#" class="text-[#333333] font-medium text-sm">Sản phẩm</a>
            <a href="#" class="text-[#333333] font-medium text-sm">Quản lý</a>
            <div class="relative">
              <button @click="showDropdown = !showDropdown" class="flex items-center justify-center w-10 h-10 rounded-full bg-white">
                <img src="~/assets/images/account-circle-icon.svg" alt="Account" class="w-5 h-5" />
              </button>
              
              <!-- Dropdown Menu -->
              <div v-if="showDropdown" class="absolute right-0 top-12 bg-white rounded-lg shadow-lg border border-gray-200 py-2 min-w-[200px] z-50">
                <a href="/account/profile" class="block px-4 py-2 text-sm text-[#333333] hover:bg-gray-50">Thông tin tài khoản</a>
                <hr class="my-1 border-gray-200" />
                <button @click="handleOpenChangePasswordModal" class="block w-full text-left px-4 py-2 text-sm text-[#333333] hover:bg-gray-50">Đổi mật khẩu</button>
                <hr class="my-1 border-gray-200" />
                <a href="#" class="block px-4 py-2 text-sm text-[#333333] hover:bg-gray-50">Đăng xuất</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-[1920px] mx-auto px-[390px] py-8">
      <!-- Content Container -->
      <div class="bg-white border border-[#E9ECEE] rounded-lg p-8 max-w-[1140px]">
        <!-- Page Title -->
        <div class="mb-8">
          <h1 class="text-2xl font-semibold text-[#1A1A1A]">Thông tin tài khoản</h1>
        </div>

        <!-- Account Information Grid -->
        <div class="space-y-6">
          <!-- Company Name -->
          <div class="flex flex-col gap-2">
            <label class="text-sm font-medium text-[#333333]">Tên công ty</label>
            <div class="text-base text-[#333333]">{{ accountInfo.companyName }}</div>
          </div>

          <!-- Tax Code -->
          <div class="flex flex-col gap-2">
            <label class="text-sm font-medium text-[#333333]">Mã số thuế</label>
            <div class="text-base text-[#333333]">{{ accountInfo.taxCode }}</div>
          </div>

          <!-- Company Address -->
          <div class="flex flex-col gap-2">
            <label class="text-sm font-medium text-[#333333]">Địa chỉ công ty</label>
            <div class="text-base text-[#333333]">{{ accountInfo.companyAddress }}</div>
          </div>

          <!-- Phone Number -->
          <div class="flex flex-col gap-2">
            <label class="text-sm font-medium text-[#333333]">Số điện thoại</label>
            <div class="text-base text-[#333333]">{{ accountInfo.phoneNumber }}</div>
          </div>

          <!-- Email -->
          <div class="flex flex-col gap-2">
            <label class="text-sm font-medium text-[#333333]">Email nhận GCN bảo hiểm</label>
            <div class="text-base text-[#333333]">{{ accountInfo.email }}</div>
          </div>
        </div>
      </div>
    </main>

    <!-- Footer -->
    <footer class="w-full bg-white border-t-[10px] border-[#0D68B2] mt-auto">
      <div class="max-w-[1920px] mx-auto px-[390px] py-8">
        <div class="flex items-center justify-center gap-[318px]">
          <!-- Logo -->
          <NuxtLink to="/" class="relative w-[130px] h-[32px] block hover:opacity-80 transition-opacity">
            <img src="~/assets/images/logo-vector-1.svg" alt="Logo 1" class="absolute" style="left: 15.69px; top: 12.58px; width: 13.99px; height: 17.08px;" />
            <img src="~/assets/images/logo-vector-2.svg" alt="Logo 2" class="absolute" style="left: 0.99px; top: 0px; width: 29.62px; height: 31.76px;" />
            <img src="~/assets/images/logo-vector-3.svg" alt="Logo 3" class="absolute" style="left: 34.58px; top: 7.94px; width: 20.19px; height: 15.78px;" />
            <img src="~/assets/images/logo-vector-4.svg" alt="Logo 4" class="absolute" style="left: 61px; top: 7.94px; width: 18.7px; height: 15.78px;" />
            <img src="~/assets/images/logo-vector-5.svg" alt="Logo 5" class="absolute" style="left: 89.57px; top: 7.94px; width: 17.55px; height: 15.88px;" />
            <img src="~/assets/images/logo-vector-6.svg" alt="Logo 6" class="absolute" style="left: 111.46px; top: 7.94px; width: 17.55px; height: 15.83px;" />
          </NuxtLink>
          
          <!-- Copyright Text -->
          <div class="text-center">
            <p class="text-sm text-[#333333] font-normal leading-[1.5em] w-[678px]">
              © 2025 Sản phẩm được phát triển bởi VNPT Lạng Sơn - Tập đoàn Bưu chính Viễn Thông Việt Nam (VNPT)
            </p>
          </div>
        </div>
      </div>
    </footer>
  </div>
  
  <!-- Change Password Modal -->
  <ChangePasswordForm
    :is-open="showChangePasswordModal"
    @close="closeChangePasswordModal"
    @success="onPasswordChangeSuccess"
  />
</template>

<script setup lang="ts">
import ChangePasswordForm from '~/components/ChangePasswordForm.vue'

useHead({
  title: 'Mua bảo hiểm - Cá nhân'
})

// Sử dụng composable để quản lý ChangePasswordModal
const {
  showChangePasswordModal,
  openChangePasswordModal,
  closeChangePasswordModal,
  onPasswordChangeSuccess
} = useChangePasswordModal()

// Reactive data
const showDropdown = ref(false)

const accountInfo = ref({
  companyName: 'Công ty vận tải Hoàng Linh',
  taxCode: '*********',
  companyAddress: '**********',
  phoneNumber: '**********',
  email: '<EMAIL>'
})

// Override openChangePasswordModal để đóng dropdown
const handleOpenChangePasswordModal = () => {
  openChangePasswordModal()
  showDropdown.value = false
}

// Close dropdown when clicking outside
onMounted(() => {
  document.addEventListener('click', (e) => {
    const target = e.target as HTMLElement
    if (!target?.closest('.relative')) {
      showDropdown.value = false
    }
  })
})
</script>

<style scoped>
/* Custom styles to match Figma design exactly */
.min-h-screen {
  min-height: 814px;
}

header {
  height: auto;
}

main {
  flex: 1;
}

footer {
  height: 121.75px;
  position: relative;
}

/* Ensure proper layout spacing */
.max-w-\[1920px\] {
  max-width: 1920px;
}

/* Shadow effects */
header {
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
}

.shadow-lg {
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.15);
}
</style>
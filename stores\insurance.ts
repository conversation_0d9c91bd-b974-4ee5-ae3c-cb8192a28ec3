import { defineStore } from 'pinia'

// Interface cho thông tin chủ xe
interface OwnerInfo {
  company_name: string
  company_address: string
  tax_number: string
  phone_number: string
  email_gcn: string
  isUpdateInfo: boolean
}

// Interface cho thông tin xe
interface VehicleInfo {
  bien_so_xe: string
  so_cho_ngoi: number
  so_khung: string
  so_may: string
  trong_tai: number
  loai_xe: string
  md_su_dung: string
  tg_bat_dau: number
  tg_ket_thuc: number
}

// Interface cho phí bảo hiểm
interface InsuranceFee {
  fee_without_vat: number
  vat_amount: number
  total_fee_with_vat: number
  total_payment: number
}

// Interface cho trạng thái quy trình
interface InsuranceState {
  currentStep: number
  ownerInfo: OwnerInfo
  vehicleInfo: VehicleInfo
  insuranceFee: InsuranceFee
  isAgreed: boolean
  orderId: number | null
  paymentStatus: 'pending' | 'success' | 'failed' | null
  isLoading: boolean
}

export const useInsuranceStore = defineStore('insurance', {
  state: (): InsuranceState => ({
    currentStep: 1,
    ownerInfo: {
      company_name: '',
      company_address: '',
      tax_number: '',
      phone_number: '',
      email_gcn: '',
      isUpdateInfo: false
    },
    vehicleInfo: {
      bien_so_xe: '',
      so_cho_ngoi: 0,
      so_khung: '',
      so_may: '',
      trong_tai: 15000, // Trên 15 tấn (cố định)
      loai_xe: 'Xe ô tô chở hàng (Xe tải)', // Cố định
      md_su_dung: 'Kinh doanh vận tải', // Cố định
      tg_bat_dau: Date.now(),
      tg_ket_thuc: Date.now() + (30 * 24 * 60 * 60 * 1000) // +30 ngày
    },
    insuranceFee: {
      fee_without_vat: 266666,
      vat_amount: 26667,
      total_fee_with_vat: 293333,
      total_payment: 293333
    },
    isAgreed: false,
    orderId: null,
    paymentStatus: null,
    isLoading: false
  }),

  getters: {
    // Kiểm tra step 1 có hợp lệ không
    isStep1Valid: (state) => {
      const owner = state.ownerInfo
      const vehicle = state.vehicleInfo
      return !!(
        owner.company_name &&
        owner.tax_number &&
        owner.company_address &&
        owner.phone_number &&
        owner.email_gcn &&
        vehicle.bien_so_xe &&
        vehicle.so_cho_ngoi > 0
      )
    },

    // Kiểm tra step 2 có hợp lệ không
    isStep2Valid: (state) => {
      return state.isAgreed
    },

    // Lấy thông tin để gửi API
    getApiPayload: (state) => {
      return {
        company_name: state.ownerInfo.company_name,
        company_address: state.ownerInfo.company_address,
        tax_number: state.ownerInfo.tax_number,
        phone_number: state.ownerInfo.phone_number,
        email_gcn: state.ownerInfo.email_gcn,
        isUpdateInfo: state.ownerInfo.isUpdateInfo,
        bien_so_xe: state.vehicleInfo.bien_so_xe,
        so_cho_ngoi: state.vehicleInfo.so_cho_ngoi,
        so_khung: state.vehicleInfo.so_khung,
        so_may: state.vehicleInfo.so_may,
        trong_tai: state.vehicleInfo.trong_tai,
        loai_xe: state.vehicleInfo.loai_xe,
        md_su_dung: state.vehicleInfo.md_su_dung,
        tg_bat_dau: Math.floor(state.vehicleInfo.tg_bat_dau / 1000), // Convert to seconds
        tg_ket_thuc: Math.floor(state.vehicleInfo.tg_ket_thuc / 1000) // Convert to seconds
      }
    },

    // Format ngày tháng để hiển thị
    formattedStartDate: (state) => {
      return new Date(state.vehicleInfo.tg_bat_dau).toLocaleDateString('vi-VN')
    },

    formattedEndDate: (state) => {
      return new Date(state.vehicleInfo.tg_ket_thuc).toLocaleDateString('vi-VN')
    },

    // Format số tiền
    formattedFees: (state) => {
      const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND'
        }).format(amount)
      }

      return {
        feeWithoutVat: formatCurrency(state.insuranceFee.fee_without_vat),
        vatAmount: formatCurrency(state.insuranceFee.vat_amount),
        totalFeeWithVat: formatCurrency(state.insuranceFee.total_fee_with_vat),
        totalPayment: formatCurrency(state.insuranceFee.total_payment)
      }
    }
  },

  actions: {
    // Cập nhật thông tin chủ xe
    updateOwnerInfo(info: Partial<OwnerInfo>) {
      this.ownerInfo = { ...this.ownerInfo, ...info }
    },

    // Cập nhật thông tin xe
    updateVehicleInfo(info: Partial<VehicleInfo>) {
      this.vehicleInfo = { ...this.vehicleInfo, ...info }
      
      // Tự động cập nhật ngày kết thúc khi thay đổi ngày bắt đầu
      if (info.tg_bat_dau) {
        this.vehicleInfo.tg_ket_thuc = info.tg_bat_dau + (30 * 24 * 60 * 60 * 1000)
      }
    },

    // Chuyển đến bước tiếp theo
    nextStep() {
      if (this.currentStep < 4) {
        this.currentStep++
      }
    },

    // Quay lại bước trước
    previousStep() {
      if (this.currentStep > 1) {
        this.currentStep--
      }
    },

    // Đi đến bước cụ thể
    goToStep(step: number) {
      if (step >= 1 && step <= 4) {
        this.currentStep = step
      }
    },

    // Toggle checkbox đồng ý
    toggleAgreement() {
      this.isAgreed = !this.isAgreed
    },

    // Lưu order ID từ API response
    setOrderId(orderId: number) {
      this.orderId = orderId
    },

    // Cập nhật trạng thái thanh toán
    setPaymentStatus(status: 'pending' | 'success' | 'failed') {
      this.paymentStatus = status
    },

    // Set loading state
    setLoading(loading: boolean) {
      this.isLoading = loading
    },

    // Reset toàn bộ store về trạng thái ban đầu
    resetStore() {
      this.currentStep = 1
      this.ownerInfo = {
        company_name: '',
        company_address: '',
        tax_number: '',
        phone_number: '',
        email_gcn: '',
        isUpdateInfo: false
      }
      this.vehicleInfo = {
        bien_so_xe: '',
        so_cho_ngoi: 0,
        so_khung: '',
        so_may: '',
        trong_tai: 15000,
        loai_xe: 'Xe ô tô chở hàng (Xe tải)',
        md_su_dung: 'Kinh doanh vận tải',
        tg_bat_dau: Date.now(),
        tg_ket_thuc: Date.now() + (30 * 24 * 60 * 60 * 1000)
      }
      this.isAgreed = false
      this.orderId = null
      this.paymentStatus = null
      this.isLoading = false
    },

    // Khởi tạo thông tin từ user đã đăng nhập
    initializeFromUser() {
      const authStore = useAuthStore()
      if (authStore.isLoggedIn && authStore.currentUser) {
        const user = authStore.currentUser
        this.updateOwnerInfo({
          company_name: user.company_name || '',
          company_address: user.company_address || '',
          tax_number: user.tax_number || '',
          phone_number: user.phone_number || '',
          email_gcn: user.email_gcn || user.username || ''
        })
      }
    }
  }
})

<template>
  <div class="bg-white relative min-h-screen">
    <!-- Main Title -->
    <div class="flex flex-col items-center gap-[9px] mt-[115px] mb-10">
      <h1 class="font-bold text-[40px] text-[#4a4a4a] text-center leading-[1.2]">
        B<PERSON><PERSON> hiểm bắt buộc trách nhiệm dân sự của chủ xe ô tô
      </h1>
      <p class="text-[16px] text-[#4a4a4a] text-center">
        Vui lòng cung cấp đầy đủ các thông tin dưới đây để tiếp tục.
      </p>
    </div>

    <!-- Progress Steps -->
    <div class="flex items-center justify-center mb-10">
      <div class="flex items-center">
        <!-- Step 1 - Active -->
        <div class="flex flex-col items-center gap-2 w-[143px]">
          <div class="w-[60px] h-[60px] bg-[#3079ff] rounded-[30px] flex items-center justify-center">
            <span class="text-white font-bold text-[28px]">1</span>
          </div>
          <div class="text-[#3079ff] font-bold text-[16px] text-center">
            Khai báo thông tin mua bảo hiểm
          </div>
        </div>

        <!-- Line 1 -->
        <div class="w-[257px] h-[2px] bg-gray-300 mx-4"></div>

        <!-- Step 2 - Inactive -->
        <div class="flex flex-col items-center gap-2 w-[143px]">
          <div class="w-[60px] h-[60px] border-2 border-[#3079ff] rounded-[30px] flex items-center justify-center">
            <span class="text-[#3079ff] font-bold text-[28px]">2</span>
          </div>
          <div class="text-black text-[16px] text-center">
            Xác nhận thông tin
          </div>
        </div>

        <!-- Line 2 -->
        <div class="w-[253px] h-[2px] bg-gray-300 mx-4"></div>

        <!-- Step 3 - Inactive -->
        <div class="flex flex-col items-center gap-2 w-[143px]">
          <div class="w-[60px] h-[60px] border-2 border-[#3079ff] rounded-[30px] flex items-center justify-center">
            <span class="text-[#3079ff] font-bold text-[28px]">3</span>
          </div>
          <div class="text-black text-[16px] text-center">
            Thanh toán
          </div>
        </div>
      </div>
    </div>

    <!-- Form Container -->
    <div class="bg-white rounded-lg border border-[#e9ecee] mx-[392px] mb-[121.75px] p-8">
      <Form @submit="onSubmit" :validation-schema="validationSchema" class="space-y-8">
        <!-- Thông tin chủ xe -->
        <div>
          <h2 class="text-[20px] font-bold text-[#333] mb-6">Thông tin chủ xe</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Tên công ty -->
            <div>
              <label class="block text-[14px] font-medium text-[#333] mb-2">
                Tên công ty <span class="text-red-500">*</span>
              </label>
              <Field
                name="company_name"
                v-model="insuranceStore.ownerInfo.company_name"
                type="text"
                class="w-full h-[54px] px-[14px] border border-[#e9ecee] rounded-[8px] focus:outline-none focus:ring-2 focus:ring-[#3079ff] focus:border-[#3079ff] text-[16px]"
                placeholder="Nhập tên công ty"
              />
              <ErrorMessage name="company_name" class="text-red-500 text-sm mt-1" />
            </div>

            <!-- Mã số thuế -->
            <div>
              <label class="block text-[14px] font-medium text-[#333] mb-2">
                Mã số thuế <span class="text-red-500">*</span>
              </label>
              <Field
                name="tax_number"
                v-model="insuranceStore.ownerInfo.tax_number"
                type="text"
                class="w-full h-[54px] px-[14px] border border-[#e9ecee] rounded-[8px] focus:outline-none focus:ring-2 focus:ring-[#3079ff] focus:border-[#3079ff] text-[16px]"
                placeholder="Nhập mã số thuế"
              />
              <ErrorMessage name="tax_number" class="text-red-500 text-sm mt-1" />
            </div>

            <!-- Địa chỉ công ty -->
            <div class="md:col-span-2">
              <label class="block text-[14px] font-medium text-[#333] mb-2">
                Địa chỉ công ty <span class="text-red-500">*</span>
              </label>
              <Field
                name="company_address"
                v-model="insuranceStore.ownerInfo.company_address"
                type="text"
                class="w-full h-[54px] px-[14px] border border-[#e9ecee] rounded-[8px] focus:outline-none focus:ring-2 focus:ring-[#3079ff] focus:border-[#3079ff] text-[16px]"
                placeholder="Nhập địa chỉ công ty"
              />
              <ErrorMessage name="company_address" class="text-red-500 text-sm mt-1" />
            </div>

            <!-- Số điện thoại -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Số điện thoại <span class="text-red-500">*</span>
              </label>
              <Field
                name="phone_number"
                v-model="insuranceStore.ownerInfo.phone_number"
                type="tel"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Nhập số điện thoại"
              />
              <ErrorMessage name="phone_number" class="text-red-500 text-sm mt-1" />
            </div>

            <!-- Email nhận GCN -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Email nhận GCN bảo hiểm <span class="text-red-500">*</span>
              </label>
              <Field
                name="email_gcn"
                v-model="insuranceStore.ownerInfo.email_gcn"
                type="email"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Nhập email nhận GCN"
              />
              <ErrorMessage name="email_gcn" class="text-red-500 text-sm mt-1" />
            </div>

            <!-- Checkbox lưu thông tin -->
            <div class="md:col-span-2">
              <label class="flex items-center">
                <input
                  type="checkbox"
                  v-model="insuranceStore.ownerInfo.isUpdateInfo"
                  class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span class="ml-2 text-sm text-gray-700">Lưu thông tin chủ xe cho lần sau</span>
              </label>
            </div>
          </div>
        </div>

        <!-- Thông tin xe -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Thông tin xe</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Biển kiểm soát -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Biển kiểm soát <span class="text-red-500">*</span>
              </label>
              <Field
                name="bien_so_xe"
                v-model="insuranceStore.vehicleInfo.bien_so_xe"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Nhập biển kiểm soát"
              />
              <ErrorMessage name="bien_so_xe" class="text-red-500 text-sm mt-1" />
            </div>

            <!-- Số chỗ ngồi -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Số chỗ ngồi <span class="text-red-500">*</span>
              </label>
              <Field
                name="so_cho_ngoi"
                v-model="insuranceStore.vehicleInfo.so_cho_ngoi"
                type="number"
                min="1"
                max="9"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Nhập số chỗ ngồi"
              />
              <ErrorMessage name="so_cho_ngoi" class="text-red-500 text-sm mt-1" />
            </div>

            <!-- Số khung -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Số khung</label>
              <Field
                name="so_khung"
                v-model="insuranceStore.vehicleInfo.so_khung"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Nhập số khung (không bắt buộc)"
              />
            </div>

            <!-- Số máy -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Số máy</label>
              <Field
                name="so_may"
                v-model="insuranceStore.vehicleInfo.so_may"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Nhập số máy (không bắt buộc)"
              />
            </div>

            <!-- Trọng tải (disabled) -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Trọng tải</label>
              <input
                type="text"
                value="Trên 15 tấn"
                disabled
                class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-500 cursor-not-allowed"
              />
            </div>

            <!-- Loại xe (disabled) -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Loại xe</label>
              <input
                type="text"
                value="Xe ô tô chở hàng (Xe tải)"
                disabled
                class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-500 cursor-not-allowed"
              />
            </div>

            <!-- Mục đích sử dụng (disabled) -->
            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-700 mb-1">Mục đích sử dụng</label>
              <input
                type="text"
                value="Kinh doanh vận tải"
                disabled
                class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-500 cursor-not-allowed"
              />
            </div>

            <!-- Ngày bắt đầu -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Ngày bắt đầu <span class="text-red-500">*</span>
              </label>
              <input
                type="datetime-local"
                v-model="startDateInput"
                @change="updateStartDate"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <!-- Ngày kết thúc (disabled) -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Ngày kết thúc</label>
              <input
                type="text"
                :value="insuranceStore.formattedEndDate"
                disabled
                class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-500 cursor-not-allowed"
              />
            </div>
          </div>
        </div>

        <!-- Buttons -->
        <div class="flex justify-end space-x-4 pt-6">
          <button
            type="submit"
            :disabled="!insuranceStore.isStep1Valid || isLoading"
            class="px-8 py-3 bg-[#3079ff] text-white rounded-[8px] hover:bg-[#2563eb] focus:outline-none focus:ring-2 focus:ring-[#3079ff] disabled:opacity-50 disabled:cursor-not-allowed font-medium text-[16px]"
          >
            <span v-if="isLoading">Đang xử lý...</span>
            <span v-else>Tiếp tục</span>
          </button>
        </div>
      </Form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Form, Field, ErrorMessage } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { useInsuranceStore } from '~/stores/insurance'

useHead({
  title: 'Khai báo thông tin mua bảo hiểm'
})

const insuranceStore = useInsuranceStore()
const isLoading = ref(false)

// Khởi tạo thông tin từ user đã đăng nhập
onMounted(() => {
  insuranceStore.initializeFromUser()
})

// Validation schema
const validationSchema = toTypedSchema(
  z.object({
    company_name: z.string().min(1, 'Tên công ty là bắt buộc').max(200, 'Tên công ty không được quá 200 ký tự'),
    tax_number: z.string().min(1, 'Mã số thuế là bắt buộc').max(200, 'Mã số thuế không được quá 200 ký tự'),
    company_address: z.string().min(1, 'Địa chỉ công ty là bắt buộc'),
    phone_number: z.string()
      .min(1, 'Số điện thoại là bắt buộc')
      .regex(/^\d{10}$/, 'Số điện thoại phải có đúng 10 chữ số'),
    email_gcn: z.string().min(1, 'Email là bắt buộc').email('Email không hợp lệ'),
    bien_so_xe: z.string().min(1, 'Biển kiểm soát là bắt buộc').max(200, 'Biển kiểm soát không được quá 200 ký tự'),
    so_cho_ngoi: z.number().min(1, 'Số chỗ ngồi phải lớn hơn 0').max(9, 'Số chỗ ngồi không được quá 9'),
    so_khung: z.string().max(200, 'Số khung không được quá 200 ký tự').optional(),
    so_may: z.string().max(200, 'Số máy không được quá 200 ký tự').optional()
  })
)

// Xử lý ngày bắt đầu
const startDateInput = ref('')

// Khởi tạo giá trị ngày bắt đầu
onMounted(() => {
  const now = new Date()
  startDateInput.value = now.toISOString().slice(0, 16) // Format: YYYY-MM-DDTHH:MM
})

const updateStartDate = () => {
  if (startDateInput.value) {
    const date = new Date(startDateInput.value)
    insuranceStore.updateVehicleInfo({
      tg_bat_dau: date.getTime()
    })
  }
}

// Xử lý submit form
const onSubmit = async () => {
  if (!insuranceStore.isStep1Valid) {
    return
  }

  isLoading.value = true
  
  try {
    // Simulate validation delay
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // Chuyển đến bước 2
    await navigateTo('/insurance/step2')
  } catch (error) {
    console.error('Error:', error)
  } finally {
    isLoading.value = false
  }
}

// Quay lại trang chủ
const goBack = () => {
  navigateTo('/')
}
</script>

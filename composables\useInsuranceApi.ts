import { useApi } from './useApi'
import { useInsuranceStore } from '~/stores/insurance'
import { useToast } from './useToast'

export const useInsuranceApi = () => {
  const insuranceStore = useInsuranceStore()
  const { post, get } = useApi()
  const { showSuccess, showError } = useToast()

  /**
   * Tạo đơn hàng mua bảo hiểm
   */
  const createInsuranceOrder = async () => {
    insuranceStore.setLoading(true)
    
    try {
      const response = await post('/v1/orders/buy-insurance', {
        body: insuranceStore.getApiPayload
      })

      if (response.data && response.data.success && response.data.code === 0) {
        // Lưu order ID
        insuranceStore.setOrderId(response.data.data.order_id)
        
        showSuccess(response.data.message || 'Khởi tạo đơn hàng thành công!')
        return {
          success: true,
          orderId: response.data.data.order_id,
          message: response.data.message
        }
      } else {
        showError(response.data?.message || 'C<PERSON> lỗi xảy ra khi tạo đơn hàng')
        return {
          success: false,
          message: response.data?.message || 'Có lỗi xảy ra khi tạo đơn hàng'
        }
      }
    } catch (error) {
      console.error('Error creating insurance order:', error)
      showError('Có lỗi xảy ra. Vui lòng thử lại.')
      return {
        success: false,
        message: 'Có lỗi xảy ra. Vui lòng thử lại.'
      }
    } finally {
      insuranceStore.setLoading(false)
    }
  }

  /**
   * Kiểm tra trạng thái thanh toán
   * @param orderId ID đơn hàng
   */
  const checkPaymentStatus = async (orderId: number) => {
    try {
      // Giả lập API kiểm tra trạng thái thanh toán
      // Trong thực tế, bạn sẽ gọi API thực tế để kiểm tra
      const response = await get(`/v1/orders/${orderId}/status`)
      
      if (response.data && response.data.success) {
        return {
          success: true,
          status: response.data.data.status,
          message: response.data.message
        }
      } else {
        return {
          success: false,
          status: 'pending',
          message: response.data?.message || 'Không thể kiểm tra trạng thái thanh toán'
        }
      }
    } catch (error) {
      console.error('Error checking payment status:', error)
      return {
        success: false,
        status: 'pending',
        message: 'Có lỗi xảy ra khi kiểm tra trạng thái thanh toán'
      }
    }
  }

  /**
   * Tạo QR code thanh toán
   * @param orderId ID đơn hàng
   */
  const generatePaymentQR = async (orderId: number) => {
    try {
      // Giả lập API tạo QR code
      // Trong thực tế, bạn sẽ gọi API của Epay để tạo QR code
      const response = await get(`/v1/orders/${orderId}/payment-qr`)
      
      if (response.data && response.data.success) {
        return {
          success: true,
          qrUrl: response.data.data.qrUrl,
          message: response.data.message
        }
      } else {
        // Fallback QR code nếu API không hoạt động
        const fallbackQrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=256x256&data=PAYMENT_${orderId}_${insuranceStore.insuranceFee.total_payment}`
        
        return {
          success: true,
          qrUrl: fallbackQrUrl,
          message: 'Đã tạo mã QR thanh toán'
        }
      }
    } catch (error) {
      console.error('Error generating payment QR:', error)
      
      // Fallback QR code nếu có lỗi
      const fallbackQrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=256x256&data=PAYMENT_${orderId}_${insuranceStore.insuranceFee.total_payment}`
      
      return {
        success: true,
        qrUrl: fallbackQrUrl,
        message: 'Đã tạo mã QR thanh toán'
      }
    }
  }

  /**
   * Lấy thông tin chi tiết đơn hàng
   * @param orderId ID đơn hàng
   */
  const getOrderDetails = async (orderId: number) => {
    try {
      const response = await get(`/v1/orders/${orderId}`)
      
      if (response.data && response.data.success) {
        return {
          success: true,
          orderDetails: response.data.data,
          message: response.data.message
        }
      } else {
        return {
          success: false,
          message: response.data?.message || 'Không thể lấy thông tin đơn hàng'
        }
      }
    } catch (error) {
      console.error('Error getting order details:', error)
      return {
        success: false,
        message: 'Có lỗi xảy ra khi lấy thông tin đơn hàng'
      }
    }
  }

  return {
    createInsuranceOrder,
    checkPaymentStatus,
    generatePaymentQR,
    getOrderDetails
  }
}

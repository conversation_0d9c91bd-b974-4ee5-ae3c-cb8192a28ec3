# **Tà<PERSON> li<PERSON>iao diện Đăng ký**

## **User Flow**

### **Flow**

- **B1:** Tại giao diện Đăng nhập, click hyperlink Đăng ký
- **B2:** Hiển thị giao diện Đăng ký tài khoản


## **<PERSON>ô tả Giao diện**

### **Email**

- **Required**
- **Textbox** - Chỉ nhập định dạng là email
- **Hint text:** Nhập Email
- **Validation:**
    - Nhập sai định dạng, hiển thị thông báo inline: *"Email chưa đúng định dạng."*
    - <PERSON>ail là unique


### **M<PERSON>t khẩu**

- **Required**
- **Textbox**
- **Hint text:** Nhập mật khẩu
- Mặc định hiển thị dưới dạng `***`
- **Icon Eye:** Click để xem mật khẩu, hiển thị trong vòng 5s, hết 5s quay lại dạng mặc định
- **Y<PERSON><PERSON> cầu:**
    - <PERSON><PERSON><PERSON> khẩu ít nhất 6 ký tự, tối đa 16 ký tự
    - Không chứa ký tự khoảng trống (space)
    - Có chứa ký tự chữ số, chữ hoa, chữ thường và ký tự đặc biệt
- **Validation:** Khi mật khẩu đặt không đúng yêu cầu, hệ thống hiển thị thông báo lỗi dưới trường Mật khẩu: *"Mật khẩu chưa đủ mạnh"*


### **Ghi chú dưới trường mật khẩu**

- Tối thiểu 6 ký tự
- Bao gồm chữ hoa, chữ thường, số và ký tự đặc biệt (ví dụ: @, \#, \$, %, \&, v.v.)


### **Xác nhận mật khẩu**

- **Required**
- **Textbox**
- **Hint text:** Nhập lại mật khẩu
- Mặc định hiển thị dưới dạng `***`
- **Icon Eye:** Click để xem mật khẩu, hiển thị trong vòng 5s, hết 5s quay lại dạng mặc định
- **Validation:** Phải trùng với mật khẩu đã được nhập, nếu không trùng hiển thị thông báo inline: *"Mật khẩu không trùng khớp."*


### **Đăng ký**

Click **Đăng ký**, kiểm tra dữ liệu dưới DB:

- Nếu đã tồn tại tài khoản email, hiển thị thông báo: *"Tài khoản đã tồn tại. Vui lòng kiểm tra lại!"*
- Nếu thỏa mãn các điều kiện, tạo tài khoản dưới DB và lưu vào CMS
- Hiển thị toast: *"Đăng ký tài khoản thành công!"* và điều hướng đến màn đăng nhập


### **Đăng nhập**

- **Hyperlink**
- Click điều hướng đến giao diện Đăng nhập

<template>
  <div class="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-6xl mx-auto">
      <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">
          🚀 useForm Advanced Demo
        </h1>
        <p class="text-lg text-gray-600">
          Khám phá các tính năng nâng cao của useForm với Zod + Vee-Validate
        </p>
      </div>

      <!-- Demo Buttons -->
      <div class="text-center mb-8 space-x-4">
        <button 
          @click="showBasicModal = true"
          class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
        >
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
          </svg>
          Basic Form
        </button>
        
        <button 
          @click="showAdvancedModal = true"
          class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors"
        >
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
          </svg>
          Advanced Form
        </button>
      </div>

      <!-- Comparison Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
        <!-- Basic Form Features -->
        <div class="bg-white rounded-lg shadow-lg p-6">
          <div class="flex items-center mb-6">
            <div class="bg-blue-100 rounded-full p-3 mr-4">
              <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <h2 class="text-2xl font-bold text-gray-900">Basic Form</h2>
          </div>
          
          <ul class="space-y-3 text-gray-600">
            <li class="flex items-start">
              <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span>Zod schema validation</span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span>TypeScript type safety</span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span>Real-time validation</span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span>Cross-field validation</span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span>Auto form reset</span>
            </li>
          </ul>
        </div>

        <!-- Advanced Form Features -->
        <div class="bg-white rounded-lg shadow-lg p-6 border-2 border-purple-200">
          <div class="flex items-center mb-6">
            <div class="bg-purple-100 rounded-full p-3 mr-4">
              <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            </div>
            <h2 class="text-2xl font-bold text-gray-900">Advanced Form</h2>
          </div>
          
          <ul class="space-y-3 text-gray-600">
            <li class="flex items-start">
              <svg class="w-5 h-5 text-purple-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span><strong>setErrors()</strong> - Set manual errors</span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-purple-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span><strong>setFieldValue()</strong> - Set field values</span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-purple-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span><strong>resetForm()</strong> - Manual form reset</span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-purple-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span><strong>values</strong> - Access current form values</span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-purple-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span>API error handling demo</span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-purple-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span>Interactive demo buttons</span>
            </li>
          </ul>
        </div>
      </div>

      <!-- useForm API Reference -->
      <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">📚 useForm API Reference</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Returned Properties</h3>
            <div class="space-y-3">
              <div class="p-3 bg-gray-50 rounded-lg">
                <code class="text-sm font-mono text-purple-600">handleSubmit</code>
                <p class="text-sm text-gray-600 mt-1">Function factory để tạo submit handler</p>
              </div>
              <div class="p-3 bg-gray-50 rounded-lg">
                <code class="text-sm font-mono text-purple-600">errors</code>
                <p class="text-sm text-gray-600 mt-1">Reactive object chứa validation errors</p>
              </div>
              <div class="p-3 bg-gray-50 rounded-lg">
                <code class="text-sm font-mono text-purple-600">values</code>
                <p class="text-sm text-gray-600 mt-1">Reactive object chứa current form values</p>
              </div>
              <div class="p-3 bg-gray-50 rounded-lg">
                <code class="text-sm font-mono text-purple-600">resetForm</code>
                <p class="text-sm text-gray-600 mt-1">Function để reset form về initial state</p>
              </div>
            </div>
          </div>
          
          <div>
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Utility Functions</h3>
            <div class="space-y-3">
              <div class="p-3 bg-gray-50 rounded-lg">
                <code class="text-sm font-mono text-purple-600">setErrors(errors)</code>
                <p class="text-sm text-gray-600 mt-1">Set manual validation errors</p>
              </div>
              <div class="p-3 bg-gray-50 rounded-lg">
                <code class="text-sm font-mono text-purple-600">setFieldValue(field, value)</code>
                <p class="text-sm text-gray-600 mt-1">Set giá trị cho một field cụ thể</p>
              </div>
              <div class="p-3 bg-gray-50 rounded-lg">
                <code class="text-sm font-mono text-purple-600">setFieldError(field, error)</code>
                <p class="text-sm text-gray-600 mt-1">Set error cho một field cụ thể</p>
              </div>
              <div class="p-3 bg-gray-50 rounded-lg">
                <code class="text-sm font-mono text-purple-600">validate()</code>
                <p class="text-sm text-gray-600 mt-1">Trigger validation manually</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal Components -->
      <ChangePasswordForm 
        :is-open="showBasicModal" 
        @close="showBasicModal = false"
        @success="onBasicSuccess"
      />
      
      <ChangePasswordFormAdvanced 
        :is-open="showAdvancedModal" 
        @close="showAdvancedModal = false"
        @success="onAdvancedSuccess"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import ChangePasswordForm from '~/components/ChangePasswordForm.vue'
import ChangePasswordFormAdvanced from '~/components/ChangePasswordFormAdvanced.vue'

useHead({
  title: 'useForm Advanced Demo - Zod + Vee-Validate'
})

const showBasicModal = ref(false)
const showAdvancedModal = ref(false)

const onBasicSuccess = () => {
  console.log('🎉 Basic form success!')
  showBasicModal.value = false
}

const onAdvancedSuccess = () => {
  console.log('🚀 Advanced form success!')
  showAdvancedModal.value = false
}
</script>

<style scoped>
code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}
</style>

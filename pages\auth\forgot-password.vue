<template>
  <div class="flex items-center justify-center min-h-screen bg-gray-50">
    <!-- Forgot Password Modal Container -->
    <div class="relative w-full max-w-[855px] h-[467px]">
      <!-- Main Modal -->
      <div class="absolute bg-white h-[467px] left-1/2 rounded-lg shadow-[0px_4px_4px_0px_rgba(0,0,0,0.15)] top-0 translate-x-[-50%] w-[855px]">
        
        <!-- Header -->
        <div class="absolute bg-[#0066b3] box-border flex flex-col gap-2.5 h-[79px] items-start justify-start left-0 px-[22px] py-[17px] rounded-tl-[8px] rounded-tr-[8px] shadow-[0px_4px_4px_0px_rgba(0,0,0,0.15)] top-0 w-[855px]">
          <div class="box-border flex flex-row items-center justify-between p-0 relative w-[802px]">
            <div class="flex flex-col font-bold h-11 justify-center leading-[0] not-italic relative text-white text-[28px] text-left w-[263px]">
              <p class="block leading-[1.2]">Quên mật khẩu</p>
            </div>
            <div class="relative size-5 cursor-pointer" @click="goBack">
              <div class="absolute inset-[-10%]">
                <svg class="block max-w-none size-full" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        <!-- Form Content -->
        <div class="absolute box-border flex flex-col gap-6 items-start justify-start left-[202px] p-0 top-[107px] w-[452px]">
          <div class="text-center mb-4">
            <p class="text-gray-600 text-sm">Nhập email của bạn để nhận liên kết đặt lại mật khẩu</p>
          </div>

          <Form class="box-border flex flex-col gap-6 items-start justify-start p-0 relative w-full" @submit="onSubmit" :validation-schema="validationSchema" v-slot="{ errors }">
            
            <!-- Email Field -->
            <div class="box-border flex flex-col gap-3 items-start justify-start p-0 relative w-full">
              <div class="font-bold h-[19px] leading-[0] not-italic overflow-ellipsis overflow-hidden relative text-black text-[16px] text-left text-nowrap w-full">
                <p class="block leading-[1.2] overflow-inherit">Email</p>
              </div>
              <div class="box-border flex flex-col h-[54px] items-start justify-start p-0 relative w-full">
                <div class="box-border flex flex-row h-[54px] items-center justify-start px-3.5 py-0 relative rounded-lg w-full border border-[#e9ecee] focus-within:border-[#0066b3]">
                  <Field
                    id="email"
                    name="email"
                    type="email"
                    class="basis-0 font-normal grow leading-[0] min-h-px min-w-px not-italic overflow-ellipsis overflow-hidden relative text-[16px] text-left text-nowrap bg-transparent border-none outline-none placeholder-[#919eab]"
                    placeholder="Nhập Email"
                  />
                </div>
                <ErrorMessage name="email" class="mt-1 text-xs text-red-600" />
              </div>
            </div>

            <!-- Submit Button -->
            <button 
              type="submit"
              :disabled="isLoading"
              class="bg-[#0d68b2] box-border flex flex-row gap-[11.69px] h-[52px] items-center justify-center px-[43.839px] py-[21.919px] relative rounded-[14.613px] shadow-[0px_10.96px_36.532px_0px_rgba(0,0,0,0.15)] w-full hover:bg-[#0056a3] disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <div v-if="isLoading" class="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
              <div class="font-bold leading-[0] not-italic text-[#f6f6f6] text-[20px] text-center">
                <p class="block leading-[normal]">{{ isLoading ? 'Đang gửi...' : 'Gửi liên kết' }}</p>
              </div>
            </button>
          </Form>

          <!-- Back to Login Link -->
          <div class="box-border flex flex-row font-normal items-center justify-center leading-[0] not-italic p-0 relative text-[16px] text-center w-full">
            <div class="overflow-ellipsis overflow-hidden relative text-black">
              <p class="leading-[1.2] overflow-inherit text-nowrap whitespace-pre">
                <NuxtLink to="/auth/login" class="text-[#4f63ee] hover:underline">← Quay lại đăng nhập</NuxtLink>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { Form, Field, ErrorMessage } from 'vee-validate';
import { toTypedSchema } from '@vee-validate/zod';
import * as z from 'zod';

useHead({
  title: 'Quên mật khẩu'
})

// Reactive variables
const isLoading = ref(false)

// Toast composable
const { showSuccess, showError } = useToast()

// Định nghĩa schema validation bằng Zod
const validationSchema = toTypedSchema(
  z.object({
    email: z.string().nonempty('Email là bắt buộc').email('Email không hợp lệ'),
  })
);

// Show toast notification
const showToastNotification = (message: string) => {
  showSuccess(message)
}

// Show error notification
const showErrorNotification = (message: string) => {
  showError(message)
}

// Go back function
const goBack = () => {
  navigateTo('/auth/login')
}

// Handle form submission
const onSubmit = async (values: any) => {
  isLoading.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // Success case - in real app, this would send reset email
    showToastNotification('Liên kết đặt lại mật khẩu đã được gửi đến email của bạn!')
    
    // Navigate back to login after success
    setTimeout(() => {
      navigateTo('/auth/login')
    }, 2000)
  } catch (error) {
    showErrorNotification('Có lỗi xảy ra. Vui lòng thử lại.')
  } finally {
    isLoading.value = false
  }
}
</script>

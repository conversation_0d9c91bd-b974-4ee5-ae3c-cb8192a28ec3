<template>
  <BaseModal
    :is-open="isOpen"
    title="Đ<PERSON><PERSON> mật kh<PERSON> (Advanced Demo)"
    size="lg"
    body-class="w-[452px] mx-auto"
    @close="closeModal"
    @open="onModalOpen"
  >
    <form @submit="onFormSubmit" class="space-y-6">
      <div class="space-y-6">
        <!-- Current Password -->
        <div class="flex flex-col gap-3">
          <label class="text-black text-base font-bold leading-[1.2]">
            M<PERSON>t khẩu hiện tại
          </label>
          <div class="relative">
            <Field name="currentPassword" type="password"
              :class="[
                'w-full h-[54px] px-[14px] border rounded-lg text-base font-normal leading-[1.2] placeholder-[#919EAB] focus:outline-none',
                errors.currentPassword ? 'border-red-500 focus:border-red-500' : 'border-[#E9ECEE] focus:border-[#0066B3]'
              ]"
              placeholder="<PERSON><PERSON>ậ<PERSON> mật kh<PERSON>u hiện tại" />
          </div>
          <ErrorMessage name="currentPassword" class="text-xs text-red-600" />
        </div>

        <!-- New Password -->
        <div class="flex flex-col gap-3">
          <label class="text-black text-base font-bold leading-[1.2]">
            Mật khẩu mới
          </label>
          <div class="relative">
            <Field name="newPassword" type="password"
              :class="[
                'w-full h-[54px] px-[14px] border rounded-lg text-base font-normal leading-[1.2] placeholder-[#919EAB] focus:outline-none',
                errors.newPassword ? 'border-red-500 focus:border-red-500' : 'border-[#E9ECEE] focus:border-[#0066B3]'
              ]"
              placeholder="Nhập mật khẩu mới" />
          </div>
          <ErrorMessage name="newPassword" class="text-xs text-red-600" />
        </div>

        <!-- Confirm New Password -->
        <div class="flex flex-col gap-3">
          <label class="text-black text-base font-bold leading-[1.2]">
            Xác nhận mật khẩu mới
          </label>
          <div class="relative">
            <Field name="confirmPassword" type="password"
              :class="[
                'w-full h-[54px] px-[14px] border rounded-lg text-base font-normal leading-[1.2] placeholder-[#919EAB] focus:outline-none',
                errors.confirmPassword ? 'border-red-500 focus:border-red-500' : 'border-[#E9ECEE] focus:border-[#0066B3]'
              ]"
              placeholder="Nhập mật khẩu mới" />
          </div>
          <ErrorMessage name="confirmPassword" class="text-xs text-red-600" />
        </div>

        <!-- Demo Buttons -->
        <div class="flex flex-col gap-2">
          <div class="text-sm text-gray-600 mb-2">Demo useForm features:</div>
          <div class="flex gap-2">
            <button type="button" @click="simulateApiError" 
              class="px-3 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200">
              Simulate API Error
            </button>
            <button type="button" @click="resetForm" 
              class="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200">
              Reset Form
            </button>
            <button type="button" @click="fillSampleData" 
              class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
              Fill Sample Data
            </button>
          </div>
        </div>

        <!-- Submit Button -->
        <button type="submit" :disabled="isLoading"
          class="w-full h-[66px] bg-gradient-to-b from-[#0D68B2] to-[#0D68B2] rounded-[14.61px] shadow-[0px_10.96px_36.53px_0px_rgba(0,0,0,0.15)] flex items-center justify-center gap-[11.69px] px-[43.84px] py-[21.92px] disabled:opacity-50 disabled:cursor-not-allowed hover:from-[#0B5A9A] hover:to-[#0B5A9A] transition-colors">
          <span v-if="isLoading" class="flex items-center gap-2">
            <svg class="animate-spin h-5 w-5 text-[#F6F6F6]" xmlns="http://www.w3.org/2000/svg" fill="none"
              viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
              </path>
            </svg>
            <span class="text-[#F6F6F6] text-xl font-bold leading-[1.22] text-center">
              Đang xử lý...
            </span>
          </span>
          <span v-else
            class="text-[#F6F6F6] text-xl font-bold leading-[1.22] text-center">
            Đổi mật khẩu
          </span>
        </button>
      </div>
    </form>
  </BaseModal>
</template>

<script setup lang="ts">
import { Field, ErrorMessage, useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import BaseModal from './BaseModal.vue'

interface Props {
  isOpen: boolean
}

interface Emits {
  close: []
  success: []
}

defineProps<Props>()
const emit = defineEmits<Emits>()

const isLoading = ref(false)

// Toast composable
const { showSuccess, showError, showInfo } = useToast()

// Định nghĩa Zod schema
const zodSchema = z.object({
  currentPassword: z.string().min(1, 'Mật khẩu hiện tại là bắt buộc'),
  newPassword: z.string()
    .min(1, 'Mật khẩu mới là bắt buộc')
    .min(6, 'Mật khẩu phải có ít nhất 6 ký tự')
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@#$%&])/,
      'Mật khẩu phải chứa ít nhất 1 chữ hoa, 1 chữ thường, 1 số và 1 ký tự đặc biệt'
    ),
  confirmPassword: z.string().min(1, 'Xác nhận mật khẩu là bắt buộc')
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: 'Mật khẩu xác nhận không khớp',
  path: ['confirmPassword']
})

type FormData = z.infer<typeof zodSchema>

// Sử dụng useForm composable
const { handleSubmit, errors, resetForm, setErrors, setFieldValue, values } = useForm({
  validationSchema: toTypedSchema(zodSchema),
  initialValues: {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  }
})

const closeModal = () => {
  emit('close')
  resetForm()
}

const onModalOpen = () => {
  resetForm()
}

// Demo function: Simulate API error
const simulateApiError = () => {
  setErrors({
    currentPassword: 'Mật khẩu hiện tại không đúng',
    newPassword: 'Mật khẩu mới không được trùng với mật khẩu cũ'
  })
}

// Demo function: Fill sample data
const fillSampleData = () => {
  setFieldValue('currentPassword', 'oldpass123')
  setFieldValue('newPassword', 'Test123@')
  setFieldValue('confirmPassword', 'Test123@')
}

// Hàm xử lý submit
const onSubmit = async (values: FormData) => {
  console.log('🚀 onSubmit called with values:', values)

  try {
    isLoading.value = true

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))

    // Simulate random API error (30% chance)
    if (Math.random() < 0.3) {
      throw new Error('API Error: Current password is incorrect')
    }

    console.log('✅ Password change request:', {
      currentPassword: values.currentPassword,
      newPassword: values.newPassword
    })

    showSuccess('🎉 Đổi mật khẩu thành công!')
    emit('success')
    closeModal()
  } catch (error) {
    console.error('❌ Error changing password:', error)

    // Set specific field errors based on API response
    setErrors({
      currentPassword: 'Mật khẩu hiện tại không đúng (từ API)'
    })
  } finally {
    isLoading.value = false
  }
}

const onFormSubmit = handleSubmit(onSubmit)
</script>

<style scoped>
.focus\:border-\[\#0066B3\]:focus {
  border-color: #0066B3;
}
</style>

# Hướng dẫn Form Đăng nhập BIC

## Tổng quan

Form đăng nhập đã được tạo theo đúng thiết kế Figma và đáp ứng đầy đủ các yêu cầu trong tài liệu URD. Form bao gồm các tính năng sau:

## Tính năng chính

### 1. Gia<PERSON> di<PERSON>
- ✅ Thiết kế theo đúng Figma design với layout modal
- ✅ Header m<PERSON><PERSON>an<PERSON> (#0066b3) với tiêu đề "Đăng nhập"
- ✅ Icon đóng (X) ở góc phải header
- ✅ Form được căn giữa với kích thước 855x467px
- ✅ Responsive design

### 2. <PERSON><PERSON><PERSON> trường nhập liệu

#### Email
- ✅ Trường bắt buộc (Required)
- ✅ Kiểu textbox với placeholder "Nhập Email"
- ✅ Validation email format
- ✅ Hiển thị lỗi khi không hợp lệ

#### Mật khẩu
- ✅ Trường bắt buộc (Required)
- ✅ Kiểu textbox với placeholder "Nhập mật khẩu"
- ✅ Mặc định hiển thị dưới dạng `***`
- ✅ Icon Eye để toggle hiển thị/ẩn mật khẩu
- ✅ Tự động ẩn mật khẩu sau 5 giây khi hiển thị
- ✅ Hiển thị lỗi khi trống

### 3. Nút Đăng nhập
- ✅ Kiểm tra thông tin tài khoản
- ✅ Hiển thị thông báo lỗi: "Email hoặc mật khẩu không đúng." khi không hợp lệ
- ✅ Hiển thị toast: "Đăng nhập thành công!" khi hợp lệ
- ✅ Điều hướng sang trang chủ sau khi đăng nhập thành công
- ✅ Loading state khi đang xử lý

### 4. Liên kết điều hướng
- ✅ Link "Đăng ký" - điều hướng đến `/auth/register`
- ✅ Link "Quên mật khẩu?" - điều hướng đến `/auth/forgot-password`

## Cấu trúc file

```
pages/auth/
├── login.vue           # Form đăng nhập chính
├── register.vue        # Form đăng ký
└── forgot-password.vue # Form quên mật khẩu
```

## Công nghệ sử dụng

- **Framework**: Nuxt.js 3
- **Validation**: Vee-validate + Zod
- **Styling**: Tailwind CSS
- **TypeScript**: Hỗ trợ đầy đủ

## Cách sử dụng

### 1. Chạy ứng dụng
```bash
npm run dev
```

### 2. Truy cập form đăng nhập
```
http://localhost:3000/auth/login
```

### 3. Test đăng nhập
Để test form, sử dụng thông tin sau:
- **Email**: <EMAIL>
- **Password**: password123

## User Flows đã implement

### Flow 1: Từ Landing page
1. Người dùng truy cập Landing page
2. Click icon Tài khoản
3. Hiển thị giao diện Đăng nhập

### Flow 2: Từ Trang chủ
1. Tại giao diện Trang chủ khi chưa đăng nhập
2. Click "Mua bảo hiểm"
3. Hiển thị giao diện Đăng nhập

## Validation Rules

### Email
- Bắt buộc nhập
- Phải đúng format email

### Mật khẩu
- Bắt buộc nhập
- Không có yêu cầu độ dài tối thiểu (theo URD)

## Thông báo

### Thành công
- Toast màu xanh hiển thị "Đăng nhập thành công!"
- Tự động ẩn sau 3 giây
- Điều hướng về trang chủ sau 1.5 giây

### Lỗi
- Toast màu đỏ hiển thị "Email hoặc mật khẩu không đúng."
- Tự động ẩn sau 3 giây

## Tính năng bảo mật

1. **Password visibility**: Tự động ẩn sau 5 giây
2. **Form validation**: Client-side validation với Zod
3. **Loading states**: Prevent double submission
4. **Error handling**: Graceful error handling

## Responsive Design

Form được thiết kế responsive và hoạt động tốt trên:
- Desktop (1024px+)
- Tablet (768px - 1023px)
- Mobile (320px - 767px)

## Accessibility

- Proper form labels
- Keyboard navigation support
- Screen reader friendly
- Focus management
- Color contrast compliance

## Tùy chỉnh

Để tùy chỉnh giao diện, chỉnh sửa các class Tailwind CSS trong file `pages/auth/login.vue`:

- Colors: `bg-[#0066b3]`, `text-[#919eab]`
- Spacing: `gap-6`, `px-3.5`, `py-0`
- Border radius: `rounded-lg`, `rounded-[14.613px]`
- Shadows: `shadow-[0px_4px_4px_0px_rgba(0,0,0,0.15)]`

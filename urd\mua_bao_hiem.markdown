# TÀI LIỆU YÊU CẦU NGƯỜI DÙNG (URD)

## Dự án: <PERSON><PERSON>ểm Trực <PERSON>ế<PERSON>
**<PERSON><PERSON><PERSON> cập nhật:** 28/07/2025  
**Người soạn:** ChatGPT (OpenAI)

---

## 1. <PERSON><PERSON><PERSON> tiê<PERSON> hệ thống
Hệ thống cho phép người dùng thực hiện việc mua bảo hiểm ô tô trực tuyến thông qua các bước nhập li<PERSON>, x<PERSON><PERSON> nh<PERSON>, thanh toán và nhận kết quả giao dịch.

---

## 2. Phạm vi
Ứng dụng Web với quy trình mua bảo hiểm gồm 4 giao diện:
- **SCR-1**: <PERSON><PERSON> báo thông tin mua bảo hiểm
- **SCR-2**: <PERSON><PERSON><PERSON> nhận thông tin
- **SCR-3**: <PERSON>h toán QR code
- **SCR-4**: Th<PERSON><PERSON> bá<PERSON> kết quả mua hàng

---

## 3. <PERSON><PERSON><PERSON><PERSON> lý chính

| Bước | Mô tả hành động |
|------|----------------|
| 1 | Tại màn hình Trang chủ, người dùng click **Mua bảo hiểm** |
| 2 | Hệ thống điều hướng tới **SCR-1: Khai báo thông tin mua bảo hiểm** |
| 3 | Người dùng nhập đầy đủ thông tin và click **Tiếp tục** |
| 4 | Hệ thống hiển thị **SCR-2: Xác nhận thông tin** |
| 5 | Người dùng xác nhận và tích chọn checkbox cam kết, click **Thanh toán** |
| 6 | Hệ thống điều hướng tới **SCR-3: Thanh toán** |
| 7 | Người dùng quét mã QR và thực hiện thanh toán |
| 8 | Hệ thống hiển thị **SCR-4: Thông báo kết quả mua hàng** |

---

## 4. Yêu cầu chi tiết theo màn hình

### SCR-1: Khai báo thông tin mua bảo hiểm

| Trường | Loại | Ràng buộc |
|--------|------|-----------|
| Tên công ty | Textbox | Required, Max 200 ký tự, báo lỗi nếu trống |
| Mã số thuế | Textbox | Required, Max 200 ký tự, báo lỗi nếu trống |
| Địa chỉ công ty | Textbox | Required, báo lỗi nếu trống |
| Số điện thoại | Textbox | Required, chỉ số, max 10 ký tự, báo lỗi nếu trống |
| Email nhận GCN bảo hiểm | Textbox | Required, định dạng email, mặc định là email tài khoản |
| Lưu thông tin chủ xe | Checkbox | Mặc định không chọn. Nếu chọn, lưu lại thông tin cho lần sau |
| Biển kiểm soát | Textbox | Required, Max 200 ký tự, báo lỗi nếu trống |
| Số chỗ | Textbox | Required, duy nhất 1 ký tự số, khác 0, báo lỗi nếu trống |
| Số khung | Textbox | Không bắt buộc, Max 200 ký tự |
| Số máy | Textbox | Không bắt buộc, Max 200 ký tự |
| Trọng tải | Textbox | *Disable*, hiển thị “Trên 15 tấn” |
| Loại xe | Textbox | *Disable*, hiển thị “Xe ô tô chở hàng (Xe tải)” |
| Mục đích sử dụng | Textbox | *Disable*, hiển thị “Kinh doanh vận tải” |
| Ngày bắt đầu | Datepicker | Required, định dạng `dd/mm/yyyy hh:mm`, mặc định thời điểm hiện tại |
| Ngày kết thúc | Datepicker | *Disable*, = Ngày bắt đầu + 30 ngày |
| Button: Tiếp tục | Button | Điều hướng sang SCR-2 |

---

### SCR-2: Xác nhận thông tin

#### Khối thông tin hiển thị:
1. **Thông tin chủ xe**  
2. **Thông tin xe**  
3. **Thời hạn bảo hiểm**  
4. **Phí**:
   - Phí chưa VAT: `266.666đ`
   - Thuế VAT: `26.667đ`
   - Tổng phí (gồm VAT): `293.333đ`
   - **TỔNG PHÍ THANH TOÁN**: `293.333đ`

#### Checkbox xác nhận:
- Nội dung: "Tôi đồng ý đã đọc, hiểu các quy định Pháp luật về Bảo hiểm bắt buộc trách nhiệm dân sự của chủ xe ô tô"
- Có link mở tab mới hiển thị Quy định bảo hiểm
- **Button: Thanh toán**
  - Chỉ **enable** nếu checkbox được chọn
  - Click chuyển sang **SCR-3**
  - **Khởi tạo đơn hàng trong DB tại bước này**

---

### SCR-3: Thanh toán
- Là giao diện thanh toán QR code tích hợp với **đối tác Epay**
- Người dùng thực hiện quét mã và thanh toán
- Hệ thống xử lý kết quả và điều hướng sang SCR-4

---

### SCR-4: Thông báo kết quả mua hàng

| Trạng thái | Nội dung hiển thị | Hành động |
|------------|--------------------|-----------|
| Thành công | Hiển thị theo figma, có nút **Quay lại trang chủ** | Click → quay về Trang chủ |
| Thất bại | Hiển thị theo figma, có nút **Quay lại trang chủ** | Click → quay về Trang chủ |

---

## 5. Ràng buộc & Quy tắc nghiệp vụ
- Email người dùng được lấy từ tài khoản đã đăng nhập
- Ngày kết thúc luôn = Ngày bắt đầu + 30 ngày
- Các trường *Disable* chỉ hiển thị, không cho sửa
- Tổng phí luôn **cố định**, không thay đổi theo input
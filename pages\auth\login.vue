<template>
  <div class="flex items-center justify-center min-h-screen bg-gray-50">
    <!-- <PERSON>gin Modal Container -->
    <div class="relative w-full max-w-[855px] h-[467px]">
      <!-- Main Modal -->
      <div class="absolute bg-white h-[467px] left-1/2 rounded-lg shadow-[0px_4px_4px_0px_rgba(0,0,0,0.15)] top-0 translate-x-[-50%] w-[855px]">

        <!-- Header -->
        <div class="absolute bg-[#0066b3] box-border flex flex-col gap-2.5 h-[79px] items-start justify-start left-0 px-[22px] py-[17px] rounded-tl-[8px] rounded-tr-[8px] shadow-[0px_4px_4px_0px_rgba(0,0,0,0.15)] top-0 w-[855px]">
          <div class="box-border flex flex-row items-center justify-between p-0 relative w-[802px]">
            <div class="flex flex-col font-bold h-11 justify-center leading-[0] not-italic relative text-white text-[28px] text-left w-[263px]">
              <p class="block leading-[1.2]">Đăng nhập</p>
            </div>
            <div class="relative size-5 cursor-pointer" @click="closeModal">
              <div class="absolute inset-[-10%]">
                <svg class="block max-w-none size-full" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        <!-- Form Content -->
        <div class="absolute box-border flex flex-col gap-6 items-start justify-start left-[202px] p-0 top-[107px] w-[452px]">
          <Form class="box-border flex flex-col gap-3 items-start justify-start p-0 relative w-full" @submit="onSubmit" :validation-schema="validationSchema" v-slot="{ errors }">

            <!-- Email Field -->
            <div class="box-border flex flex-col gap-3 items-start justify-start p-0 relative w-full">
              <div class="font-bold h-[19px] leading-[0] not-italic overflow-ellipsis overflow-hidden relative text-black text-[16px] text-left text-nowrap w-full">
                <p class="block leading-[1.2] overflow-inherit">Email</p>
              </div>
              <div class="box-border flex flex-col h-[54px] items-start justify-start p-0 relative w-full">
                <div class="box-border flex flex-row h-[54px] items-center justify-start px-3.5 py-0 relative rounded-lg w-full border border-[#e9ecee] focus-within:border-[#0066b3]">
                  <Field
                    id="email"
                    name="email"
                    type="email"
                    class="basis-0 font-normal grow leading-[0] min-h-px min-w-px not-italic overflow-ellipsis overflow-hidden relative text-[16px] text-left text-nowrap bg-transparent border-none outline-none placeholder-[#919eab]"
                    placeholder="Nhập Email"
                  />
                </div>
                <ErrorMessage name="email" class="mt-1 text-xs text-red-600" />
              </div>
            </div>

            <!-- Password Field -->
            <div class="box-border flex flex-col gap-3 items-start justify-start p-0 relative w-full">
              <div class="font-bold h-[19px] leading-[0] not-italic overflow-ellipsis overflow-hidden relative text-black text-[16px] text-left text-nowrap w-full">
                <p class="block leading-[1.2] overflow-inherit">Mật khẩu</p>
              </div>
              <div class="box-border flex flex-col h-[54px] items-start justify-start p-0 relative w-full">
                <div class="box-border flex flex-row h-[54px] items-center justify-start px-3.5 py-0 relative rounded-lg w-full border border-[#e9ecee] focus-within:border-[#0066b3]">
                  <Field
                    id="password"
                    name="password"
                    :type="showPassword ? 'text' : 'password'"
                    class="basis-0 font-normal grow leading-[0] min-h-px min-w-px not-italic overflow-ellipsis overflow-hidden relative text-[16px] text-left text-nowrap bg-transparent border-none outline-none placeholder-[#919eab]"
                    placeholder="Nhập mật khẩu"
                  />
                  <div class="absolute box-border flex flex-row h-10 items-center justify-center p-0 right-0 top-1/2 translate-y-[-50%] cursor-pointer" @click="togglePasswordVisibility">
                    <svg v-if="!showPassword" class="w-5 h-5 text-[#919eab]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    <svg v-else class="w-5 h-5 text-[#919eab]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                    </svg>
                  </div>
                </div>
                <ErrorMessage name="password" class="mt-1 text-xs text-red-600" />
              </div>
            </div>

            <!-- Login Button -->
            <button
              type="submit"
              :disabled="isLoading"
              class="bg-[#0d68b2] box-border flex flex-row gap-[11.69px] h-[52px] items-center justify-center px-[43.839px] py-[21.919px] relative rounded-[14.613px] shadow-[0px_10.96px_36.532px_0px_rgba(0,0,0,0.15)] w-full hover:bg-[#0056a3] disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <div v-if="isLoading" class="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
              <div class="font-bold leading-[0] not-italic text-[#f6f6f6] text-[20px] text-center">
                <p class="block leading-[normal]">{{ isLoading ? 'Đang xử lý...' : 'Đăng nhập' }}</p>
              </div>
            </button>
          </Form>

          <!-- Links -->
          <div class="box-border flex flex-row font-normal items-start justify-between leading-[0] not-italic p-0 relative text-[16px] text-center text-nowrap w-full">
            <div class="overflow-ellipsis overflow-hidden relative text-black">
              <p class="leading-[1.2] overflow-inherit text-nowrap whitespace-pre">
                <span>Chưa có tài khoản? </span>
                <NuxtLink to="/auth/register" class="text-[#4f63ee] hover:underline">Đăng ký</NuxtLink>
              </p>
            </div>
            <div class="overflow-ellipsis overflow-hidden relative">
              <NuxtLink to="/auth/forgot-password" class="text-[#4f63ee] hover:underline leading-[1.2] text-[16px] text-nowrap whitespace-pre">
                Quên mật khẩu?
              </NuxtLink>
            </div>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { Form, Field, ErrorMessage } from 'vee-validate';
import { toTypedSchema } from '@vee-validate/zod';
import * as z from 'zod';

useHead({
  title: 'Đăng nhập'
})

// Reactive variables
const showPassword = ref(false)
const isLoading = ref(false)
const passwordVisibilityTimer = ref<NodeJS.Timeout | null>(null)

// Toast composable
const { showSuccess, showError } = useToast()

// Định nghĩa schema validation bằng Zod
const validationSchema = toTypedSchema(
  z.object({
    email: z.string().nonempty('Email là bắt buộc').email('Email không hợp lệ'),
    password: z.string().nonempty('Mật khẩu là bắt buộc'),
  })
);

// Toggle password visibility with 5-second auto-hide
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value

  // Clear existing timer
  if (passwordVisibilityTimer.value) {
    clearTimeout(passwordVisibilityTimer.value)
  }

  // Auto-hide password after 5 seconds if showing
  if (showPassword.value) {
    passwordVisibilityTimer.value = setTimeout(() => {
      showPassword.value = false
      passwordVisibilityTimer.value = null
    }, 5000)
  }
}

// Show toast notification
const showToastNotification = (message: string) => {
  showSuccess(message)
}

// Show error notification
const showErrorNotification = (message: string) => {
  showError(message)
}

// Close modal function (for future use when integrated into a modal system)
const closeModal = () => {
  // This can be used when the login form is used as a modal
  // For now, we can navigate back or to home page
  navigateTo('/')
}

// Handle form submission
const onSubmit = async (values: any) => {
  isLoading.value = true

  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Mock validation - replace with actual API call
    const mockValidCredentials = {
      email: '<EMAIL>',
      password: 'password123'
    }

    if (values.email === mockValidCredentials.email && values.password === mockValidCredentials.password) {
      // Success case
      showToastNotification('Đăng nhập thành công!')

      // Navigate to home page after successful login
      setTimeout(() => {
        navigateTo('/')
      }, 1500)
    } else {
      // Error case
      showErrorNotification('Email hoặc mật khẩu không đúng.')
    }
  } catch (error) {
    showErrorNotification('Có lỗi xảy ra. Vui lòng thử lại.')
  } finally {
    isLoading.value = false
  }
}

// Cleanup timer on component unmount
onUnmounted(() => {
  if (passwordVisibilityTimer.value) {
    clearTimeout(passwordVisibilityTimer.value)
  }
})
</script>

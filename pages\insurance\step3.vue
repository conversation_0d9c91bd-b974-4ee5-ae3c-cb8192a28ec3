<template>
  <div class="bg-white relative min-h-screen">
    <!-- Main Title -->
    <div class="flex flex-col items-center gap-[9px] mt-[115px] mb-10">
      <h1 class="font-bold text-[40px] text-[#4a4a4a] text-center leading-[1.2]">
        <PERSON><PERSON> to<PERSON> bảo hiểm
      </h1>
      <p class="text-[16px] text-[#4a4a4a] text-center">
        <PERSON><PERSON> chuyển hướng đến cổng thanh toán...
      </p>
    </div>

    <!-- Payment Processing -->
    <div class="bg-white rounded-lg border border-[#e9ecee] mx-[392px] mb-[121.75px] p-8 text-center">
      <div class="mb-8">
        <div class="w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-[#3079ff]"></div>
        </div>
        <h2 class="text-[24px] font-bold text-[#333] mb-4"><PERSON><PERSON><PERSON> lý than<PERSON> toán</h2>
        <p class="text-[16px] text-[#666] mb-6">
          Bạn sẽ được chuyển hướng đến cổng thanh toán trong giây lát...
        </p>
      </div>

      <!-- Order Info -->
      <div class="bg-gray-50 rounded-lg p-6 mb-6 text-left">
        <h3 class="text-[18px] font-bold text-[#333] mb-4">Thông tin đơn hàng</h3>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <span class="text-[14px] text-[#666]">Mã đơn hàng:</span>
            <p class="font-medium">#{{ insuranceStore.orderId }}</p>
          </div>
          <div>
            <span class="text-[14px] text-[#666]">Biển số xe:</span>
            <p class="font-medium">{{ insuranceStore.vehicleInfo.bien_so_xe }}</p>
          </div>
          <div>
            <span class="text-[14px] text-[#666]">Tên công ty:</span>
            <p class="font-medium">{{ insuranceStore.ownerInfo.company_name }}</p>
          </div>
          <div>
            <span class="text-[14px] text-[#666]">Số tiền:</span>
            <p class="font-bold text-[#3079ff] text-[18px]">{{ insuranceStore.formattedFees.totalPayment }}</p>
          </div>
        </div>
      </div>

      <!-- Manual redirect button if auto redirect fails -->
      <button v-if="showManualButton" @click="redirectToPayment"
        class="px-8 py-3 bg-[#3079ff] text-white rounded-[8px] hover:bg-[#2563eb] focus:outline-none focus:ring-2 focus:ring-[#3079ff] font-medium text-[16px]">
        Tiến hành thanh toán
      </button>
    </div>

  </div>
</template>

<script setup lang="ts">
import { useInsuranceStore } from '~/stores/insurance'

useHead({
  title: 'Thanh toán bảo hiểm'
})

const insuranceStore = useInsuranceStore()
const showManualButton = ref(false)

// Kiểm tra nếu chưa có order ID
onMounted(() => {
  if (!insuranceStore.orderId) {
    navigateTo('/insurance/step2')
    return
  }

  // Tự động redirect sau 3 giây
  setTimeout(() => {
    redirectToPayment()
  }, 3000)

  // Hiển thị nút manual sau 5 giây nếu chưa redirect
  setTimeout(() => {
    showManualButton.value = true
  }, 5000)
})

// Redirect đến trang thanh toán của bên thứ 3
const redirectToPayment = async () => {
  try {
    // Trong thực tế, bạn sẽ gọi API để lấy URL thanh toán
    // Ở đây chúng ta giả lập bằng cách tạo một URL giả

    // Giả lập URL thanh toán
    const paymentUrl = `https://payment.example.com/pay?order_id=${insuranceStore.orderId}&amount=${insuranceStore.insuranceFee.total_payment}`

    // Trong thực tế, bạn sẽ redirect đến URL này
    console.log('Redirecting to payment URL:', paymentUrl)

    // Giả lập thanh toán thành công và chuyển đến step4 sau 2 giây
    setTimeout(() => {
      insuranceStore.setPaymentStatus('success')
      navigateTo('/insurance/step4')
    }, 2000)
  } catch (error) {
    console.error('Error redirecting to payment:', error)
    showManualButton.value = true
  }
}

// Quay lại bước 2
const goBack = () => {
  navigateTo('/insurance/step2')
}
</script>

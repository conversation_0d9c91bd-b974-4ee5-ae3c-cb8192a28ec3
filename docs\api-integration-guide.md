# API Integration Guide - BIC Login

## Tổng quan

Đã tích hợp thành công API login thực tế vào LoginModal component. Hệ thống sử dụng:

- ✅ **Real API**: `https://**********:9911/v1/auth/login`
- ✅ **useApi Composable**: Centralized API handling
- ✅ **Environment Variables**: API URL và API Key từ .env
- ✅ **Auth Store**: Lưu trữ token và user info
- ✅ **Direct API Call**: Không thông qua auth store login method

## Cấu hình API

### 1. Environment Variables (.env)

```env
NUXT_PUBLIC_API_BASE_URL=https://**********:9911
NUXT_PUBLIC_API_KEY=BTC128M8093ILKR584ERM147IAGJ2IX2
GEMINI_API_KEY=AIzaSyBbZVPsLbKE_qfomhNxzlDx4TWnD1SkGiQ
```

### 2. Nuxt Config (nuxt.config.ts)

```typescript
runtimeConfig: {
  public: {
    apiBaseUrl: process.env.NUXT_PUBLIC_API_BASE_URL || 'https://api.example.com',
    apiKey: process.env.NUXT_PUBLIC_API_KEY || '',
  }
}
```

### 3. useApi Composable Updates

<augment_code_snippet path="composables/useApi.ts" mode="EXCERPT">
```typescript
const getConfig = () => {
  const auth = useAuthStore();
  const config = useRuntimeConfig();
  
  let config_header: { [key: string]: string } = {
    'x-api-key': config.public.apiKey as string,
    'Content-Type': 'application/json'
  };

  if (auth.token) {
    config_header = {
      ...config_header,
      Authorization: `Bearer ${auth.token}`,
    };
  }

  return config_header;
};
```
</augment_code_snippet>

## API Specification

### Login Endpoint

**URL**: `POST /v1/auth/login`

**Headers**:
```
x-api-key: BTC128M8093ILKR584ERM147IAGJ2IX2
Content-Type: application/json
```

**Request Body**:
```json
{
  "username": "<EMAIL>",
  "password": "Vivas@123"
}
```

**Response Success**:
```json
{
  "success": true,
  "statusCode": 200,
  "code": 0,
  "message": "Đăng nhập thành công!",
  "data": {
    "accessToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzM4NCJ9...",
    "refreshToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzM4NCJ9...",
    "userInfo": {
      "id": 1,
      "username": "<EMAIL>",
      "company_name": "Công ty TNHH Cung cấp Giải pháp Dịch vụ Giá trị gia tăng",
      "company_address": "Số 115 Đường Trần Duy Hưng, Phường Yên Hòa, Thành phố Hà Nội, Việt Nam",
      "tax_number": "02437558989",
      "phone_number": "",
      "email_gcn": "",
      "birthday": "",
      "sex": 0,
      "sex_name": "Không xác định",
      "status": 10,
      "status_name": "Hoạt động"
    }
  }
}
```

## Auth Store Updates

### 1. User Interface

<augment_code_snippet path="stores/auth.ts" mode="EXCERPT">
```typescript
interface User {
  id: number
  username: string
  company_name: string
  company_address: string
  tax_number: string
  phone_number: string
  email_gcn: string
  birthday: string
  sex: number
  sex_name: string
  status: number
  status_name: string
}
```
</augment_code_snippet>

### 2. Save Login Data Method

<augment_code_snippet path="stores/auth.ts" mode="EXCERPT">
```typescript
// Save login data from API response
saveLoginData(loginResponse: any) {
  const { accessToken, refreshToken, userInfo } = loginResponse.data
  
  // Update store state
  this.user = userInfo
  this.token = accessToken
  this.isAuthenticated = true
  
  // Store in localStorage
  if (import.meta.client) {
    localStorage.setItem('auth_token', accessToken)
    localStorage.setItem('refresh_token', refreshToken)
    localStorage.setItem('auth_user', JSON.stringify(userInfo))
  }
  
  return { success: true, user: userInfo }
}
```
</augment_code_snippet>

### 3. Updated Getters

```typescript
getters: {
  currentUser: (state) => state.user,
  isLoggedIn: (state) => state.isAuthenticated && !!state.user,
  userEmail: (state) => state.user?.username || '',
  userFullName: (state) => state.user?.company_name || '',
}
```

## LoginModal Integration

### 1. Form Schema Update

<augment_code_snippet path="components/auth/LoginModal.vue" mode="EXCERPT">
```typescript
// Zod schema - changed from email to username
const zodSchema = z.object({
  username: z.string().min(1, 'Email là bắt buộc').email('Email không hợp lệ'),
  password: z.string().min(1, 'Mật khẩu là bắt buộc'),
})
```
</augment_code_snippet>

### 2. API Call Implementation

<augment_code_snippet path="components/auth/LoginModal.vue" mode="EXCERPT">
```typescript
// Form submission
const onSubmit = async (values: FormData) => {
  isLoading.value = true
  
  try {
    const { post } = useApi()
    const authStore = useAuthStore()
    
    // Call login API
    const response = await post('/v1/auth/login', {
      body: {
        username: values.username,
        password: values.password
      }
    })
    
    if (response.data && response.data.success) {
      // Save login data to store
      const result = authStore.saveLoginData(response.data)
      
      showToastNotification(response.data.message || 'Đăng nhập thành công!')
      
      setTimeout(() => {
        emit('loginSuccess', result.user)
        closeModal()
      }, 1500)
    } else {
      showErrorNotification(response.data?.message || 'Đăng nhập thất bại.')
    }
  } catch (error: any) {
    console.error('Login error:', error)
    showErrorNotification('Email hoặc mật khẩu không đúng.')
  } finally {
    isLoading.value = false
  }
}
```
</augment_code_snippet>

## Data Flow

### 1. Login Process

```
User Input → LoginModal → useApi → Real API → Response → Auth Store → LocalStorage
```

### 2. Detailed Steps

1. **User submits form** in LoginModal
2. **LoginModal calls** `useApi().post('/v1/auth/login')`
3. **useApi sends request** with x-api-key header
4. **API returns** success response with tokens and userInfo
5. **LoginModal calls** `authStore.saveLoginData(response.data)`
6. **Auth Store saves** token, refreshToken, userInfo to state and localStorage
7. **LoginModal shows** success toast and closes modal
8. **Header updates** to show user info

### 3. Token Storage

```
localStorage:
- auth_token: accessToken (for API authorization)
- refresh_token: refreshToken (for token refresh)
- auth_user: JSON.stringify(userInfo) (user data)
```

## Testing

### 1. Test Credentials

```
Username: <EMAIL>
Password: Vivas@123
```

### 2. Test Steps

1. **Open**: `http://localhost:3002`
2. **Click**: Icon Tài khoản → Đăng nhập
3. **Enter**: Test credentials above
4. **Submit**: Form should call real API
5. **Verify**: Success toast and modal closes
6. **Check**: Header shows user company name

### 3. Debug Tools

```javascript
// Check localStorage
console.log('Token:', localStorage.getItem('auth_token'))
console.log('User:', JSON.parse(localStorage.getItem('auth_user')))

// Check auth store
const authStore = useAuthStore()
console.log('Store state:', authStore.$state)
```

## Error Handling

### 1. API Errors

- **Network Error**: "Có lỗi xảy ra. Vui lòng thử lại."
- **Invalid Credentials**: "Email hoặc mật khẩu không đúng."
- **Server Error**: API response message or fallback

### 2. Validation Errors

- **Empty Username**: "Email là bắt buộc"
- **Invalid Email**: "Email không hợp lệ"
- **Empty Password**: "Mật khẩu là bắt buộc"

## Security Features

### 1. Headers

- **x-api-key**: API authentication
- **Content-Type**: application/json
- **Authorization**: Bearer token (after login)

### 2. Token Management

- **Access Token**: Stored for API calls
- **Refresh Token**: Stored for token renewal
- **Auto-clear**: On logout or error

### 3. HTTPS

- **Production**: Use HTTPS for API calls
- **Development**: HTTP acceptable for testing

## Next Steps

1. **Implement Refresh Token**: Auto-refresh expired tokens
2. **Add Logout API**: Call logout endpoint
3. **Error Handling**: Improve error messages
4. **Loading States**: Better UX during API calls
5. **Retry Logic**: Handle network failures

API integration hoàn tất với real endpoint và proper token management! 🚀
